import { ApiProperty } from "@nestjs/swagger";
import { <PERSON><PERSON><PERSON><PERSON>, IsDateString, IsInt, IsNotEmpty, IsOptional, IsString, Matches } from "class-validator";
import { a<PERSON><PERSON><PERSON><PERSON> } from "viem/chains";


export class CreateApiKeyDto {
  @ApiProperty({ example: 'My second API Key'})
  @IsNotEmpty()
  @IsString()
  key_name: string;

  @ApiProperty({ example: 1})
  @IsNotEmpty()
  @IsInt()
  user_id: number;

  @ApiProperty({ example: 1})
  @IsOptional()
  @IsInt()
  permissions_id: number;

  @ApiProperty({ example: true})
  @IsOptional()
  @IsInt()
  is_active: boolean;

  @ApiProperty({ example: '' })
  @IsOptional()
  @IsDateString()
  @Matches(/^\d{4}-\d{2}-\d{2}$/, { message: 'expires_at must be in YYYY-MM-DD format' })
  expires_at: string;
}