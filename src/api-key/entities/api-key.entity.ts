import { Column, CreateDateColumn, <PERSON><PERSON>ty, PrimaryGeneratedColumn, UpdateDateColumn } from "typeorm";

@Entity('api_keys')
export class ApiKey {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'varchar', unique: true })
  api_key: string;

  @Column({ type: 'varchar', })
  secret_key: string; // hashed

  @Column({ type: 'varchar', })
  key_name: string;

  @Column({ type: 'int' })
  user_id: number;

  @Column({ type: 'int', nullable: true })
  permissions_id: number;

  @Column({ type: 'boolean', default: true })
  is_active: boolean;

  @Column({ type: 'timestamp', nullable: true })
  expires_at: Date | null;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

}

