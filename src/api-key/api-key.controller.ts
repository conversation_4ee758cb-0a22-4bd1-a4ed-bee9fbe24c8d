import { <PERSON>, Get, Post, Body, Patch, Param, Delete } from '@nestjs/common';
import { ApiKeyService } from './api-key.service';
import { CreateApiKeyDto } from './dto/create-api-key.dto';
import { UpdateApiKeyDto } from './dto/update-api-key.dto';

@Controller('api-key')
export class ApiKeyController {
  constructor(private readonly apiKeyService: ApiKeyService) {}

    // POST /api-keys → Create new API key
    @Post('create')
    async create(@Body() createApiDto: CreateApiKeyDto) {
      console.log("creating API key")
      return await this.apiKeyService.create(createApiDto);
    }

    // GET /api-keys → List all API keys for current user
    @Get('getAllKeys/:userId')
    async getAllUserKeys(@Param('userId') userId: number) {
      console.log("getting all existing apikeys")
      return await this.apiKeyService.getAllUserKeys(userId);
    }

    // DELETE /api-keys/:id → Delete API key
    @Delete('remove/:id')
    async remove(@Param('id') id: number) {
      console.log("deleting api key")
      return await this.apiKeyService.remove(id);
    }

    // PATCH /api-keys/:id → Update key name, permissions, or status
    @Patch('update/:id')
    async update(@Param('id') id: number, @Body() updateApiKeyDto: UpdateApiKeyDto) {
      console.log("updating api key")
      return await this.apiKeyService.update(id, updateApiKeyDto);
    }

  }