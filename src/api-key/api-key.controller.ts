import { Controller, Get, Post, Body } from '@nestjs/common';
import { ApiKeyService } from './api-key.service';
import { CreateApiKeyDto } from './dto/create-api-key.dto';
import { UpdateApiKeyDto } from './dto/update-api-key.dto';

@Controller('api-key')
export class ApiKeyController {
  constructor(private readonly apiKeyService: ApiKeyService) {}

    // POST /api-keys → Create new API key
    @Post('create')
    async create(@Body() createApiDto: CreateApiKeyDto) {
      console.log("creating API key")
      return await this.apiKeyService.create(createApiDto);
    }

    // GET /api-keys → List all API keys for current user
    @Post('getAllKeys')
    async getAllUserKeys(@Body() body: { userId: number }) {
      console.log("getting all existing apikeys")
      return await this.apiKeyService.getAllUserKeys(body.userId);
    }

    // DELETE /api-keys/:id → Delete API key (soft delete)
    @Post('remove')
    async remove(@Body() body: { id: number }) {
      console.log("deleting api key")
      return await this.apiKeyService.remove(body.id);
    }

    // PATCH /api-keys/:id → Update key name, permissions, or status
    @Post('update')
    async update(@Body() body: { id: number; updateData: UpdateApiKeyDto }) {
      console.log("updating api key")
      return await this.apiKeyService.update(body.id, body.updateData);
    }

    // PATCH /api-keys/regenerateSecret/:id → delete old secret generate and return new one
    @Post('regenerateNewSecret')
    async regenerateNewSecret(@Body() body: { id: number }){
      return await this.apiKeyService.regenerateNewSecret(body.id);
    }
}